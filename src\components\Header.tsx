import React, { useState } from 'react';
import { Bo<PERSON>, Menu, X } from 'lucide-react';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-black/10 backdrop-blur-md border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-3">
            <div className="bg-white/20 p-2 rounded-lg backdrop-blur-sm">
              <Bot className="h-6 w-6 text-white" />
            </div>
            <span className="text-lg font-semibold text-white">IA Solutions</span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#servicios" className="text-white/80 hover:text-white transition-colors text-sm font-medium">
              Servicios
            </a>
            <a href="#contacto" className="text-white/80 hover:text-white transition-colors text-sm font-medium">
              Contacto
            </a>
            <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 backdrop-blur-sm border border-white/20">
              Consulta Gratuita
            </button>
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="text-white hover:text-white/80 transition-colors p-2"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMenuOpen && (
          <div className="md:hidden absolute top-16 left-0 right-0 bg-black/10 border-b border-white/30 shadow-lg">
            <nav className="px-4 py-6 space-y-4">
              <a 
                href="#servicios" 
                onClick={closeMenu}
                className="block text-gray-800 hover:text-gray-900 transition-colors text-base font-medium py-2"
              >
                Servicios
              </a>
              <a 
                href="#contacto" 
                onClick={closeMenu}
                className="block text-gray-800 hover:text-gray-900 transition-colors text-base font-medium py-2"
              >
                Contacto
              </a>
              <button 
                onClick={closeMenu}
                className="w-full bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white px-4 py-3 rounded-full text-base font-medium transition-all duration-200 shadow-lg mt-4"
              >
                Consulta Gratuita
              </button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}