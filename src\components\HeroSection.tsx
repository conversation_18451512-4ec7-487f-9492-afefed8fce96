import React from 'react';
import { ArrowR<PERSON>, Sparkles } from 'lucide-react';

export default function HeroSection() {
  return (
    <section className="relative min-h-screen bg-white text-blue-800 overflow-hidden flex items-center">
      {/* SVG Animated Background */}
      <div className="absolute inset-0 w-full h-full overflow-hidden">
        <svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur in="SourceGraphic" stdDeviation="40" />
            </filter>
            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur in="SourceGraphic" stdDeviation="20" />
            </filter>
          </defs>

          {/* Continuous bubble stream - Layer 1 */}
          <g filter="url(#blur)">
            {/* Burbujas que suben desde abajo */}
            <circle cx="15%" r="120" fill="#60A5FA" opacity="0.6">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="10s"
                repeatCount="indefinite"
                begin="0s"
              />
              <animate
                attributeName="opacity"
                values="0;0.6;0.6;0"
                dur="10s"
                repeatCount="indefinite"
                begin="0s"
              />
            </circle>
            <circle cx="15%" r="120" fill="#60A5FA" opacity="0.6">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="10s"
                repeatCount="indefinite"
                begin="-3.33s"
              />
              <animate
                attributeName="opacity"
                values="0;0.6;0.6;0"
                dur="10s"
                repeatCount="indefinite"
                begin="-3.33s"
              />
            </circle>
            <circle cx="15%" r="120" fill="#60A5FA" opacity="0.6">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="10s"
                repeatCount="indefinite"
                begin="-6.67s"
              />
              <animate
                attributeName="opacity"
                values="0;0.6;0.6;0"
                dur="10s"
                repeatCount="indefinite"
                begin="-6.67s"
              />
            </circle>

            <circle cx="85%" r="150" fill="#3B82F6" opacity="0.5">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="15s"
                repeatCount="indefinite"
                begin="0s"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="15s"
                repeatCount="indefinite"
                begin="0s"
              />
            </circle>
            <circle cx="85%" r="150" fill="#3B82F6" opacity="0.5">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="15s"
                repeatCount="indefinite"
                begin="-5s"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="15s"
                repeatCount="indefinite"
                begin="-5s"
              />
            </circle>
            <circle cx="85%" r="150" fill="#3B82F6" opacity="0.5">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="15s"
                repeatCount="indefinite"
                begin="-10s"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="15s"
                repeatCount="indefinite"
                begin="-10s"
              />
            </circle>

            <circle cx="45%" r="100" fill="#1D4ED8" opacity="0.4">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="8s"
                repeatCount="indefinite"
                begin="0s"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="8s"
                repeatCount="indefinite"
                begin="0s"
              />
            </circle>
            <circle cx="45%" r="100" fill="#1D4ED8" opacity="0.4">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="8s"
                repeatCount="indefinite"
                begin="-2.67s"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="8s"
                repeatCount="indefinite"
                begin="-2.67s"
              />
            </circle>
            <circle cx="45%" r="100" fill="#1D4ED8" opacity="0.4">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="8s"
                repeatCount="indefinite"
                begin="-5.33s"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="8s"
                repeatCount="indefinite"
                begin="-5.33s"
              />
            </circle>

            <circle cx="25%" r="180" fill="#1E40AF" opacity="0.3">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="12s"
                repeatCount="indefinite"
                begin="0s"
              />
              <animate
                attributeName="opacity"
                values="0;0.3;0.3;0"
                dur="12s"
                repeatCount="indefinite"
                begin="0s"
              />
            </circle>
            <circle cx="25%" r="180" fill="#1E40AF" opacity="0.3">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="12s"
                repeatCount="indefinite"
                begin="-4s"
              />
              <animate
                attributeName="opacity"
                values="0;0.3;0.3;0"
                dur="12s"
                repeatCount="indefinite"
                begin="-4s"
              />
            </circle>
            <circle cx="25%" r="180" fill="#1E40AF" opacity="0.3">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="12s"
                repeatCount="indefinite"
                begin="-8s"
              />
              <animate
                attributeName="opacity"
                values="0;0.3;0.3;0"
                dur="12s"
                repeatCount="indefinite"
                begin="-8s"
              />
            </circle>

            <circle cx="70%" r="110" fill="#2563EB" opacity="0.4">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="10s"
                repeatCount="indefinite"
                begin="-2s"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="10s"
                repeatCount="indefinite"
                begin="-2s"
              />
            </circle>
            <circle cx="70%" r="110" fill="#2563EB" opacity="0.4">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="10s"
                repeatCount="indefinite"
                begin="-5.33s"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="10s"
                repeatCount="indefinite"
                begin="-5.33s"
              />
            </circle>
            <circle cx="70%" r="110" fill="#2563EB" opacity="0.4">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="10s"
                repeatCount="indefinite"
                begin="-8.67s"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="10s"
                repeatCount="indefinite"
                begin="-8.67s"
              />
            </circle>
          </g>

          {/* Continuous bubble stream - Layer 2 (smaller bubbles) */}
          <g filter="url(#glow)">
            <circle cx="35%" r="70" fill="#DBEAFE" opacity="0.4">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="8s"
                repeatCount="indefinite"
                begin="-1s"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="8s"
                repeatCount="indefinite"
                begin="-1s"
              />
            </circle>
            <circle cx="35%" r="70" fill="#DBEAFE" opacity="0.4">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="8s"
                repeatCount="indefinite"
                begin="-3.67s"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="8s"
                repeatCount="indefinite"
                begin="-3.67s"
              />
            </circle>
            <circle cx="35%" r="70" fill="#DBEAFE" opacity="0.4">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="8s"
                repeatCount="indefinite"
                begin="-6.33s"
              />
              <animate
                attributeName="opacity"
                values="0;0.4;0.4;0"
                dur="8s"
                repeatCount="indefinite"
                begin="-6.33s"
              />
            </circle>

            <circle cx="80%" r="60" fill="#BFDBFE" opacity="0.5">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="10s"
                repeatCount="indefinite"
                begin="-3s"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="10s"
                repeatCount="indefinite"
                begin="-3s"
              />
            </circle>
            <circle cx="80%" r="60" fill="#BFDBFE" opacity="0.5">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="10s"
                repeatCount="indefinite"
                begin="-6.33s"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="10s"
                repeatCount="indefinite"
                begin="-6.33s"
              />
            </circle>
            <circle cx="80%" r="60" fill="#BFDBFE" opacity="0.5">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="10s"
                repeatCount="indefinite"
                begin="-9.67s"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="10s"
                repeatCount="indefinite"
                begin="-9.67s"
              />
            </circle>

            <circle cx="10%" r="80" fill="#93C5FD" opacity="0.3">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="12s"
                repeatCount="indefinite"
                begin="-4s"
              />
              <animate
                attributeName="opacity"
                values="0;0.3;0.3;0"
                dur="12s"
                repeatCount="indefinite"
                begin="-4s"
              />
            </circle>
            <circle cx="10%" r="80" fill="#93C5FD" opacity="0.3">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="12s"
                repeatCount="indefinite"
                begin="-8s"
              />
              <animate
                attributeName="opacity"
                values="0;0.3;0.3;0"
                dur="12s"
                repeatCount="indefinite"
                begin="-8s"
              />
            </circle>

            <circle cx="55%" r="90" fill="#1D4ED8" opacity="0.5">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="15s"
                repeatCount="indefinite"
                begin="-2.5s"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="15s"
                repeatCount="indefinite"
                begin="-2.5s"
              />
            </circle>
            <circle cx="55%" r="90" fill="#1D4ED8" opacity="0.5">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="15s"
                repeatCount="indefinite"
                begin="-7.5s"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="15s"
                repeatCount="indefinite"
                begin="-7.5s"
              />
            </circle>
            <circle cx="55%" r="90" fill="#1D4ED8" opacity="0.5">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,100vh; 0,-100vh"
                dur="15s"
                repeatCount="indefinite"
                begin="-12.5s"
              />
              <animate
                attributeName="opacity"
                values="0;0.5;0.5;0"
                dur="15s"
                repeatCount="indefinite"
                begin="-12.5s"
              />
            </circle>
          </g>
        </svg>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 pt-24">
        <div className="text-center animate-fade-in">
          <div className="inline-flex items-center space-x-2 bg-blue-600/10 backdrop-blur-sm px-4 py-2 rounded-full mb-8 border border-blue-600/20">
            <Sparkles className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">Líder en Soluciones de IA</span>
          </div>
          
          <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold mb-8 leading-tight text-blue-800">
            Transformamos tu Negocio con{' '}
            <span className="bg-gradient-to-r from-blue-600 via-blue-500 to-blue-400 bg-clip-text text-transparent">
              Inteligencia Artificial
            </span>
          </h1>
          
          <p className="text-xl sm:text-2xl text-blue-800/80 mb-12 max-w-4xl mx-auto leading-relaxed font-light">
            Automatización inteligente, agentes de IA y soluciones personalizadas 
            para impulsar tu empresa hacia el futuro
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
              <span>Consulta Gratuita</span>
              <ArrowRight className="h-5 w-5" />
            </button>
            <button className="bg-white hover:bg-gray-100 text-blue-800 px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 border border-blue-200">
              Ver Casos de Éxito
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
