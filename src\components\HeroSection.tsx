import React from 'react';
import { <PERSON><PERSON><PERSON>, Spark<PERSON> } from 'lucide-react';

export default function HeroSection() {
  return (
    <section className="relative min-h-screen bg-white text-blue-800 overflow-hidden flex items-center">
      {/* SVG Animated Background */}
      <div className="absolute inset-0 w-full h-full overflow-hidden">
        <svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur in="SourceGraphic" stdDeviation="40" />
            </filter>
            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur in="SourceGraphic" stdDeviation="20" />
            </filter>
          </defs>

          {/* Continuous bubble stream - Layer 1 */}
          <g filter="url(#blur)">
            {/* First set of bubbles */}
            <circle className="animate-bubble-rise animate-bubble-sway" cx="15%" cy="100%" r="120" fill="#60A5FA" opacity="0.6" style={{ animationDelay: '0s' }} />
            <circle className="animate-bubble-rise animate-bubble-sway" cx="15%" cy="100%" r="120" fill="#60A5FA" opacity="0.6" style={{ animationDelay: '-5s' }} />
            <circle className="animate-bubble-rise animate-bubble-sway" cx="15%" cy="100%" r="120" fill="#60A5FA" opacity="0.6" style={{ animationDelay: '-10s' }} />

            <circle className="animate-bubble-rise-slow animate-bubble-sway-reverse" cx="85%" cy="100%" r="150" fill="#3B82F6" opacity="0.5" style={{ animationDelay: '0s' }} />
            <circle className="animate-bubble-rise-slow animate-bubble-sway-reverse" cx="85%" cy="100%" r="150" fill="#3B82F6" opacity="0.5" style={{ animationDelay: '-6.67s' }} />
            <circle className="animate-bubble-rise-slow animate-bubble-sway-reverse" cx="85%" cy="100%" r="150" fill="#3B82F6" opacity="0.5" style={{ animationDelay: '-13.33s' }} />

            <circle className="animate-bubble-rise-fast animate-bubble-sway" cx="45%" cy="100%" r="100" fill="#1D4ED8" opacity="0.4" style={{ animationDelay: '0s' }} />
            <circle className="animate-bubble-rise-fast animate-bubble-sway" cx="45%" cy="100%" r="100" fill="#1D4ED8" opacity="0.4" style={{ animationDelay: '-4s' }} />
            <circle className="animate-bubble-rise-fast animate-bubble-sway" cx="45%" cy="100%" r="100" fill="#1D4ED8" opacity="0.4" style={{ animationDelay: '-8s' }} />

            <circle className="animate-bubble-rise-delayed animate-bubble-sway-reverse" cx="25%" cy="100%" r="180" fill="#1E40AF" opacity="0.3" style={{ animationDelay: '0s' }} />
            <circle className="animate-bubble-rise-delayed animate-bubble-sway-reverse" cx="25%" cy="100%" r="180" fill="#1E40AF" opacity="0.3" style={{ animationDelay: '-6s' }} />
            <circle className="animate-bubble-rise-delayed animate-bubble-sway-reverse" cx="25%" cy="100%" r="180" fill="#1E40AF" opacity="0.3" style={{ animationDelay: '-12s' }} />

            <circle className="animate-bubble-rise animate-bubble-sway" cx="70%" cy="100%" r="110" fill="#2563EB" opacity="0.4" style={{ animationDelay: '-2s' }} />
            <circle className="animate-bubble-rise animate-bubble-sway" cx="70%" cy="100%" r="110" fill="#2563EB" opacity="0.4" style={{ animationDelay: '-7s' }} />
            <circle className="animate-bubble-rise animate-bubble-sway" cx="70%" cy="100%" r="110" fill="#2563EB" opacity="0.4" style={{ animationDelay: '-12s' }} />
          </g>

          {/* Continuous bubble stream - Layer 2 (smaller bubbles) */}
          <g filter="url(#glow)">
            <circle className="animate-bubble-rise-fast animate-bubble-sway" cx="35%" cy="100%" r="70" fill="#DBEAFE" opacity="0.4" style={{ animationDelay: '-1s' }} />
            <circle className="animate-bubble-rise-fast animate-bubble-sway" cx="35%" cy="100%" r="70" fill="#DBEAFE" opacity="0.4" style={{ animationDelay: '-5s' }} />
            <circle className="animate-bubble-rise-fast animate-bubble-sway" cx="35%" cy="100%" r="70" fill="#DBEAFE" opacity="0.4" style={{ animationDelay: '-9s' }} />

            <circle className="animate-bubble-rise animate-bubble-sway-reverse" cx="80%" cy="100%" r="60" fill="#BFDBFE" opacity="0.5" style={{ animationDelay: '-3s' }} />
            <circle className="animate-bubble-rise animate-bubble-sway-reverse" cx="80%" cy="100%" r="60" fill="#BFDBFE" opacity="0.5" style={{ animationDelay: '-8s' }} />
            <circle className="animate-bubble-rise animate-bubble-sway-reverse" cx="80%" cy="100%" r="60" fill="#BFDBFE" opacity="0.5" style={{ animationDelay: '-13s' }} />

            <circle className="animate-bubble-rise-delayed animate-bubble-sway" cx="10%" cy="100%" r="80" fill="#93C5FD" opacity="0.3" style={{ animationDelay: '-4s' }} />
            <circle className="animate-bubble-rise-delayed animate-bubble-sway" cx="10%" cy="100%" r="80" fill="#93C5FD" opacity="0.3" style={{ animationDelay: '-10s' }} />
            <circle className="animate-bubble-rise-delayed animate-bubble-sway" cx="10%" cy="100%" r="80" fill="#93C5FD" opacity="0.3" style={{ animationDelay: '-16s' }} />

            <circle className="animate-bubble-rise-slow animate-bubble-sway-reverse" cx="55%" cy="100%" r="90" fill="#1D4ED8" opacity="0.5" style={{ animationDelay: '-2.5s' }} />
            <circle className="animate-bubble-rise-slow animate-bubble-sway-reverse" cx="55%" cy="100%" r="90" fill="#1D4ED8" opacity="0.5" style={{ animationDelay: '-9.17s' }} />
            <circle className="animate-bubble-rise-slow animate-bubble-sway-reverse" cx="55%" cy="100%" r="90" fill="#1D4ED8" opacity="0.5" style={{ animationDelay: '-15.83s' }} />
          </g>
        </svg>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 pt-24">
        <div className="text-center animate-fade-in">
          <div className="inline-flex items-center space-x-2 bg-blue-600/10 backdrop-blur-sm px-4 py-2 rounded-full mb-8 border border-blue-600/20">
            <Sparkles className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">Líder en Soluciones de IA</span>
          </div>
          
          <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold mb-8 leading-tight text-blue-800">
            Transformamos tu Negocio con{' '}
            <span className="bg-gradient-to-r from-blue-600 via-blue-500 to-blue-400 bg-clip-text text-transparent">
              Inteligencia Artificial
            </span>
          </h1>
          
          <p className="text-xl sm:text-2xl text-blue-800/80 mb-12 max-w-4xl mx-auto leading-relaxed font-light">
            Automatización inteligente, agentes de IA y soluciones personalizadas 
            para impulsar tu empresa hacia el futuro
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
              <span>Consulta Gratuita</span>
              <ArrowRight className="h-5 w-5" />
            </button>
            <button className="bg-white hover:bg-gray-100 text-blue-800 px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 border border-blue-200">
              Ver Casos de Éxito
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
