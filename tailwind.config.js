/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
      },
      colors: {
        blue: {
          800: '#2C5282',
          700: '#2A4A7C',
          600: '#2563EB',
          500: '#4299E1',
          400: '#90CDF4',
        },
        green: {
          500: '#25D366',
          600: '#128C7E',
        }
      },
      animation: {
        'fade-in': 'fadeIn 0.8s ease-out',
        'fade-in-up': 'fadeInUp 0.8s ease-out',
        'float': 'float 6s ease-in-out infinite',
        'float-slow': 'float 8s ease-in-out infinite',
        'float-slower': 'float 10s ease-in-out infinite',
        'pulse-glow': 'pulseGlow 4s ease-in-out infinite',
        'drift': 'drift 15s ease-in-out infinite',
        'drift-reverse': 'driftReverse 12s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeInUp: {
          '0%': {
            opacity: '0',
            transform: 'translateY(30px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          },
        },
        float: {
          '0%, 100%': {
            transform: 'translateY(0px) scale(1)',
            opacity: '0.5'
          },
          '50%': {
            transform: 'translateY(-20px) scale(1.05)',
            opacity: '0.8'
          },
        },
        pulseGlow: {
          '0%, 100%': {
            opacity: '0.4',
            transform: 'scale(1)'
          },
          '50%': {
            opacity: '0.8',
            transform: 'scale(1.1)'
          },
        },
        drift: {
          '0%, 100%': {
            transform: 'translateX(0px) translateY(0px)'
          },
          '25%': {
            transform: 'translateX(10px) translateY(-15px)'
          },
          '50%': {
            transform: 'translateX(-5px) translateY(-10px)'
          },
          '75%': {
            transform: 'translateX(-10px) translateY(5px)'
          },
        },
        driftReverse: {
          '0%, 100%': {
            transform: 'translateX(0px) translateY(0px)'
          },
          '25%': {
            transform: 'translateX(-15px) translateY(10px)'
          },
          '50%': {
            transform: 'translateX(8px) translateY(15px)'
          },
          '75%': {
            transform: 'translateX(12px) translateY(-8px)'
          },
        },
      },
    },
  },
  plugins: [],
};